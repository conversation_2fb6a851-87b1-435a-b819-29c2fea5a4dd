<template>
  <div class="products" :class="{ loaded: isLoaded }">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-overlay"></div>
        <div class="hero-particles">
          <div v-for="i in 12" :key="i" :class="`particle particle-${i}`"></div>
        </div>
        <div class="hero-grid">
          <div v-for="i in 20" :key="i" class="grid-item"></div>
        </div>
      </div>

      <div class="container">
        <div class="hero-content">
          <div class="hero-badge fade-in-up">
            <Icon icon="mdi:package-variant-closed" class="badge-icon" />
            <span>企业级智慧水利产品矩阵</span>
          </div>

          <h1 class="hero-title fade-in-up delay-1">
            <span class="title-line">全方位数字化</span>
            <span class="title-line title-highlight">水利产品生态</span>
          </h1>

          <p class="hero-subtitle fade-in-up delay-2">
            融合IoT、大数据、AI、云计算等前沿技术<br />
            <strong>构建完整的智慧水利产品体系，赋能行业数字化转型</strong>
          </p>

          <div class="hero-stats fade-in-up delay-3">
            <div class="stat-item">
              <div class="stat-icon">
                <Icon icon="mdi:package-variant" />
              </div>
              <div class="stat-number" data-count="3">0</div>
              <div class="stat-label">核心产品</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-icon">
                <Icon icon="mdi:chart-line" />
              </div>
              <div class="stat-number" data-count="100">0</div>
              <div class="stat-label">成功案例</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-icon">
                <Icon icon="mdi:star" />
              </div>
              <div class="stat-number" data-count="99">0</div>
              <div class="stat-label">满意度%</div>
            </div>
          </div>
        </div>

        <div class="hero-visual fade-in-up delay-4">
          <div class="visual-container">
            <div class="central-hub">
              <div class="hub-core">
                <Icon icon="mdi:water" class="core-icon" />
                <div class="core-pulse"></div>
              </div>
              <div class="hub-ring ring-outer"></div>
              <div class="hub-ring ring-middle"></div>
              <div class="hub-ring ring-inner"></div>
            </div>

            <div class="product-constellation">
              <div class="constellation-node node-1">
                <div class="node-icon">
                  <Icon icon="mdi:chart-line" />
                </div>
                <div class="node-label">数据分析</div>
              </div>
              <div class="constellation-node node-2">
                <div class="node-icon">
                  <Icon icon="mdi:shield-check" />
                </div>
                <div class="node-label">安全监控</div>
              </div>
              <div class="constellation-node node-3">
                <div class="node-icon">
                  <Icon icon="mdi:cloud-outline" />
                </div>
                <div class="node-label">云端服务</div>
              </div>
              <div class="constellation-node node-4">
                <div class="node-icon">
                  <Icon icon="mdi:trending-up" />
                </div>
                <div class="node-label">智能预测</div>
              </div>
              <div class="constellation-node node-5">
                <div class="node-icon">
                  <Icon icon="mdi:water-pump" />
                </div>
                <div class="node-label">设备管理</div>
              </div>
              <div class="constellation-node node-6">
                <div class="node-icon">
                  <Icon icon="mdi:chart-multiple" />
                </div>
                <div class="node-label">综合调度</div>
              </div>
            </div>

            <div class="connection-web">
              <svg class="connection-svg" viewBox="0 0 400 400">
                <g class="connections">
                  <path class="connection-path" d="M200,200 L120,80" />
                  <path class="connection-path" d="M200,200 L320,100" />
                  <path class="connection-path" d="M200,200 L350,240" />
                  <path class="connection-path" d="M200,200 L280,320" />
                  <path class="connection-path" d="M200,200 L120,320" />
                  <path class="connection-path" d="M200,200 L60,180" />
                </g>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 产品分类导航 -->
    <section class="categories-section">
      <div class="container">
        <div class="categories-header">
          <h2 class="categories-title">产品分类</h2>
          <p class="categories-subtitle">专业化产品线，满足不同场景需求</p>
        </div>

        <div class="categories-grid">
          <button
            v-for="category in categories"
            :key="category.id"
            :class="[
              'category-card',
              { active: activeCategory === category.id },
            ]"
            @click="setActiveCategory(category.id)"
          >
            <div class="category-visual">
              <div class="category-icon">
                <Icon :icon="category.icon" />
              </div>
              <div class="category-bg"></div>
            </div>
            <div class="category-content">
              <h3 class="category-name">{{ category.name }}</h3>
              <p class="category-desc">{{ category.description }}</p>
              <div class="category-count">{{ category.count }}+ 产品</div>
            </div>
            <div class="category-indicator">
              <Icon icon="mdi:arrow-right" />
            </div>
          </button>
        </div>
      </div>
    </section>

    <!-- 产品展示区域 -->
    <section class="products-showcase">
      <div class="container">
        <div class="showcase-header">
          <h2 class="showcase-title">
            {{
              categories.find((c) => c.id === activeCategory)?.name ||
              "全部产品"
            }}
          </h2>
          <div class="showcase-meta">
            <span class="product-count"
              >{{ filteredProducts.length }} 个产品</span
            >
            <div class="view-toggle">
              <button
                :class="['toggle-btn', { active: viewMode === 'grid' }]"
                @click="viewMode = 'grid'"
              >
                <Icon icon="mdi:view-grid" />
              </button>
              <button
                :class="['toggle-btn', { active: viewMode === 'list' }]"
                @click="viewMode = 'list'"
              >
                <Icon icon="mdi:view-list" />
              </button>
            </div>
          </div>
        </div>

        <div :class="['products-grid', viewMode]">
          <div
            v-for="product in filteredProducts"
            :key="product.id"
            class="product-card"
            @click="handleProductDetail(product)"
          >
            <div class="card-visual">
              <div class="product-icon">
                <Icon :icon="product.icon" />
              </div>
              <div class="product-badge" :class="product.priority">
                {{ product.badge }}
              </div>
              <div class="card-gradient"></div>
            </div>

            <div class="card-content">
              <div class="product-header">
                <h3 class="product-title">{{ product.title }}</h3>
                <div
                  class="product-status"
                  :class="product.status.toLowerCase()"
                >
                  <Icon :icon="product.statusIcon" class="status-icon" />
                  <span>{{ product.status }}</span>
                </div>
              </div>

              <p class="product-description">{{ product.description }}</p>

              <div class="product-features">
                <div class="features-title">核心功能</div>
                <div class="features-list">
                  <div
                    class="feature"
                    v-for="feature in product.features.slice(0, 3)"
                    :key="feature"
                  >
                    <Icon icon="mdi:check-circle" class="feature-icon" />
                    <span>{{ feature }}</span>
                  </div>
                  <div v-if="product.features.length > 3" class="feature-more">
                    +{{ product.features.length - 3 }} 更多功能
                  </div>
                </div>
              </div>

              <div class="product-tags">
                <span
                  class="tag"
                  v-for="tag in product.tags.slice(0, 3)"
                  :key="tag"
                  >{{ tag }}</span
                >
                <span v-if="product.tags.length > 3" class="tag-more"
                  >+{{ product.tags.length - 3 }}</span
                >
              </div>
            </div>

            <div class="card-footer">
              <button class="detail-btn">
                <span>查看详情</span>
                <Icon icon="mdi:arrow-right" class="btn-arrow" />
              </button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredProducts.length === 0" class="empty-state">
          <div class="empty-visual">
            <Icon icon="mdi:package-variant" class="empty-icon" />
            <div class="empty-animation">
              <div class="animation-dot"></div>
              <div class="animation-dot"></div>
              <div class="animation-dot"></div>
            </div>
          </div>
          <h3 class="empty-title">该分类下暂无产品</h3>
          <p class="empty-description">
            我们正在不断完善产品线，敬请期待更多创新产品
          </p>
          <button class="empty-action" @click="setActiveCategory('all')">
            <span>查看全部产品</span>
            <Icon icon="mdi:arrow-right" />
          </button>
        </div>
      </div>
    </section>

    <!-- 产品优势 -->
    <section class="advantages-section">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:star" />
            <span>核心优势</span>
          </div>
          <h2 class="section-title">为什么选择我们的产品</h2>
          <p class="section-subtitle">
            专业技术 × 创新理念 × 服务保障 = 卓越产品体验
          </p>
        </div>

        <div class="advantages-grid">
          <div
            class="advantage-card"
            v-for="(advantage, index) in advantages"
            :key="index"
          >
            <div class="advantage-visual">
              <div class="advantage-number">
                {{ String(index + 1).padStart(2, "0") }}
              </div>
              <div class="advantage-icon">
                <Icon :icon="advantage.icon" />
              </div>
              <div class="visual-bg"></div>
            </div>
            <div class="advantage-content">
              <h3 class="advantage-title">{{ advantage.title }}</h3>
              <p class="advantage-description">{{ advantage.description }}</p>
              <div class="advantage-details">
                <div
                  class="detail"
                  v-for="detail in advantage.details"
                  :key="detail"
                >
                  <Icon icon="mdi:arrow-right-circle" class="detail-icon" />
                  <span>{{ detail }}</span>
                </div>
              </div>
            </div>
            <div class="advantage-decorator"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA 区域 -->
    <section class="cta-section">
      <div class="cta-background">
        <div class="cta-overlay"></div>
        <div class="cta-pattern">
          <div v-for="i in 50" :key="i" class="pattern-dot"></div>
        </div>
      </div>

      <div class="container">
        <div class="cta-content">
          <div class="cta-text">
            <div class="cta-badge">
              <Icon icon="mdi:handshake" />
              <span>专业服务</span>
            </div>
            <h2 class="cta-title">找到适合您的解决方案了吗？</h2>
            <p class="cta-subtitle">
              我们的技术专家团队将为您提供<strong>个性化的产品咨询</strong>和<strong
                >定制化解决方案</strong
              ><br />
              助力您的数字化转型之路
            </p>
            <div class="cta-features">
              <div class="cta-feature">
                <Icon icon="mdi:account-tie" />
                <span>1对1专家咨询</span>
              </div>
              <div class="cta-feature">
                <Icon icon="mdi:clock" />
                <span>24小时快速响应</span>
              </div>
              <div class="cta-feature">
                <Icon icon="mdi:shield-check" />
                <span>方案质量保证</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 产品详情模态框 -->
    <div v-if="showProductDetail" class="product-detail-modal" @click="closeProductDetail">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title">{{ selectedProduct?.title }}</h2>
          <button class="modal-close" @click="closeProductDetail">
            <Icon icon="mdi:close" />
          </button>
        </div>

        <div class="modal-body">
          <div class="product-detail-hero">
            <div class="detail-icon">
              <Icon :icon="selectedProduct?.icon" />
            </div>
            <div class="detail-badge" :class="selectedProduct?.priority">
              {{ selectedProduct?.badge }}
            </div>
          </div>

          <div class="product-detail-content">
            <div class="detail-section">
              <h3>产品介绍</h3>
              <p class="detail-description">{{ getProductDescription(selectedProduct) }}</p>
            </div>

            <div class="detail-section">
              <h3>产品亮点</h3>
              <div class="highlights-grid">
                <div
                  v-for="highlight in getProductHighlights(selectedProduct)"
                  :key="highlight.title"
                  class="highlight-item"
                >
                  <div class="highlight-icon">
                    <Icon :icon="highlight.icon" />
                  </div>
                  <div class="highlight-content">
                    <h4>{{ highlight.title }}</h4>
                    <p>{{ highlight.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="detail-section">
              <h3>核心功能</h3>
              <div class="features-grid">
                <div
                  v-for="feature in selectedProduct?.features"
                  :key="feature"
                  class="feature-item"
                >
                  <Icon icon="mdi:check-circle" class="feature-check" />
                  <span>{{ feature }}</span>
                </div>
              </div>
            </div>

            <div class="detail-section">
              <h3>技术标签</h3>
              <div class="tags-container">
                <span
                  v-for="tag in selectedProduct?.tags"
                  :key="tag"
                  class="detail-tag"
                >
                  {{ tag }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="btn-secondary" @click="closeProductDetail">
            <span>关闭</span>
          </button>
          <button class="btn-primary" @click="handleConsultation">
            <Icon icon="mdi:account-tie" />
            <span>咨询产品</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Icon } from "@iconify/vue";
import { ref, computed, onMounted } from "vue";

defineOptions({
  name: "Products",
});

// 页面状态
const isLoaded = ref(false);
const activeCategory = ref("all");
const viewMode = ref("grid");
const showProductDetail = ref(false);
const selectedProduct = ref(null);

// 产品分类数据
const categories = ref([
  {
    id: "all",
    name: "全部产品",
    icon: "mdi:view-grid",
    description: "完整产品矩阵",
    count: 3,
  },
  {
    id: "warning",
    name: "防汛预警",
    icon: "mdi:flood",
    description: "防汛四预系统",
    count: 1,
  },
  {
    id: "management",
    name: "资源管理",
    icon: "mdi:water-pump",
    description: "水资源调配",
    count: 1,
  },
  {
    id: "assistant",
    name: "智能助手",
    icon: "mdi:robot",
    description: "河湖智能助手",
    count: 1,
  },
]);

// 产品数据
const allProducts = ref([
  {
    id: 1,
    title: '防汛"四预"产品',
    description:
      "智能化防汛决策应用产品，通过实时监测雨水情，提前预测洪水风险，第一时间发出警报，模拟多种洪水影响和处置方案，滚动更新应急响应方案，把传统被动救灾转变为主动防控",
    icon: "mdi:flood",
    category: "warning",
    priority: "urgent",
    badge: "核心产品",
    status: "稳定版",
    statusIcon: "mdi:check-circle",
    features: [
      "融合预报",
      "智能预警",
      "仿真预演",
      "滚动预案",
      "河湖助手贴心服务",
      "国产架构安全可控",
    ],
    tags: ["多元数据融合", "智算模型", "AI大模型", "微服务架构", "移动应用"],
  },
  {
    id: 2,
    title: "水资源调配产品",
    description:
      "聚焦实施国家节水行动、落实水资源刚性约束的要求，围绕取用水监管服务、水资源综合调度、节水管理等方面建设场景化协同应用，实现水资源及节水监管和服务的整体智治",
    icon: "mdi:water-pump",
    category: "warning",
    priority: "urgent",
    badge: "核心产品",
    status: "稳定版",
    statusIcon: "mdi:check-circle",
    features: [
      "水资源驾驶舱",
      "水资源业务管理",
      "水资源信息服务",
      "水资源调配支持",
      "水资源智能助手",
      "国产化安全架构",
    ],
    tags: ["水资源数字账本", "优化配置", "节水管理", "微服务架构"],
  },
  {
    id: 3,
    title: "河湖智能助手产品",
    description:
      "智能化河湖综合管护应用产品，通过汇聚空天地水工立体监测数据，动态构建河湖知识库，结合智能模型的推理分析、图像识别、语义理解、视频生成等多模态处理能力，有效提升河湖综合治理能力",
    icon: "mdi:robot",
    category: "assistant",
    priority: "high",
    badge: "AI助手",
    status: "运行中",
    statusIcon: "mdi:check-circle",
    features: [
      "河湖知识问答",
      "问题智能交办",
      "巡查任务规划",
      "河长履职辅助",
      "河湖动态立体感知",
      "智能模型应用支撑",
    ],
    tags: ["河湖大百科", "立体感知", "智能模型", "国产架构"],
  },
]);

// 优势数据
const advantages = ref([
  {
    icon: "mdi:database",
    title: "多元数据融合",
    description: "实现自然地理、防洪工程、雨水情测报等各类数据融合",
    details: [
      "流域数字孪生体构建",
      "多维数据综合分析",
      "直观展示风险态势",
      "全方位信息整合",
    ],
  },
  {
    icon: "mdi:brain",
    title: "智能算法驱动",
    description: "基于AI大模型和智能算法提供精准预测和决策支持",
    details: [
      "气象水文预报模型",
      "智能预警算法",
      "风险评估模型",
      "优化配置算法",
    ],
  },
  {
    icon: "mdi:shield-check-outline",
    title: "国产架构安全",
    description: "采用微服务框架，适配国产软硬件环境，完全自主可控",
    details: [
      "国产操作系统适配",
      "微服务架构设计",
      "标准服务接口",
      "移动应用支持",
    ],
  },
  {
    icon: "mdi:account-tie",
    title: "专业服务保障",
    description: "提供全方位的专业技术服务和定制化解决方案",
    details: [
      "专家团队支持",
      "个性化定制开发",
      "7×24小时技术支持",
      "持续优化升级",
    ],
  },
]);

// 计算属性 - 过滤产品
const filteredProducts = computed(() => {
  if (activeCategory.value === "all") {
    return allProducts.value;
  }
  return allProducts.value.filter(
    (product) => product.category === activeCategory.value
  );
});

// 方法
const setActiveCategory = (categoryId) => {
  activeCategory.value = categoryId;
};

const handleProductDetail = (product) => {
  selectedProduct.value = product;
  showProductDetail.value = true;
};

const closeProductDetail = () => {
  showProductDetail.value = false;
  selectedProduct.value = null;
};

// 获取产品详细描述
const getProductDescription = (product) => {
  if (!product) return '';

  const descriptions = {
    1: "防汛\"四预\"产品是一款智能化防汛决策应用产品，通过实时监测雨水情，提前预测洪水风险（预报），第一时间向相关部门和群众发出警报（预警），同时模拟多种洪水影响和可行的处置方案（预演），滚动更新应急响应方案（预案），把传统被动救灾转变为主动防控，让防汛工作更科学、更高效。通过融合预报、智能预警、仿真预演、滚动预案的全流程智能管理，大幅提升防汛决策效率和减灾能力，同时结合AI大模型打造防汛\"智能助手\"，进一步赋能水灾害防控体系，帮助减少洪灾损失，提升应急响应速度，为各级政府防汛部门提供科学依据以及决策支撑。",
    2: "水资源调配应用产品，为聚焦实施国家节水行动、落实水资源刚性约束的要求，以全面提升水资源管理、保障和服务能力为总体目标，围绕取用水监管服务、水资源综合调度、节水管理等方面建设场景化协同应用，通过集成物联网、大数据、人工智能等前沿技术，实现水资源及节水监管和服务的整体智治，强化水资源监控预警能力和取用水过程动态管控能力，切实提升水资源的精细化管理与智慧化服务水平，科学指导推进节水行动，支撑最严格水资源管理制度的长效实施，实现水资源保障治理体系和治理能力的现代化。",
    3: "河湖智能助手是一款智能化河湖综合管护应用产品，通过汇聚空天地水工立体监测数据，以及人工巡查、问题上报、任务处置等业务过程数据，动态构建河湖知识库，结合智能模型的推理分析、图像识别、语义理解、视频生成等多模态处理能力，形成对河湖水量水质、问题态势、河长履职等较为全面的认知，在此基础上进一步实现问题交办处置、巡查任务规划、河长考核监督等决策和行动方案，有效提升河湖综合治理能力，大幅减轻基层河长巡查管护压力，维护河湖美丽健康生态。"
  };

  return descriptions[product.id] || product.description;
};

// 获取产品亮点
const getProductHighlights = (product) => {
  if (!product) return [];

  const highlights = {
    1: [
      {
        icon: "mdi:database",
        title: "多元数据融合可视",
        description: "实现自然地理情况、防洪工程体系、雨水情测报体系和防御工作体系等各类数据的融合，形成流域数字孪生体，直观展示水利工程、保护对象和灾害隐患的分布情况。"
      },
      {
        icon: "mdi:brain",
        title: "智算模型精准预警",
        description: "内置气象预报和水文预报模型，24小时滚动预报区域降雨和河道水情，支撑流域洪涝风险科学预测和研判；结合气象、水文、巡查等多维数据，综合生成预警信息。"
      },
      {
        icon: "mdi:robot",
        title: "河湖助手贴心服务",
        description: "基于防汛\"四预\"知识库和预案库，开发防汛\"四预\"智能助手，在系统理解流域防汛管理工作基础上，支持用户以自然语言交互方式筛选和查询数据。"
      },
      {
        icon: "mdi:shield-check",
        title: "国产架构安全可控",
        description: "采用微服务框架开发，可灵活升级和扩展服务；适配国产操作系统、中间件和数据库，完全自主可控；提供标准服务接口方便系统对接和数据集成。"
      }
    ],
    2: [
      {
        icon: "mdi:book-open",
        title: "水资源数字账本",
        description: "汇聚水源地监管、取用水监管、用水量核定等业务生成数据，建立从水源地到水龙头的动态水账本，直观呈现区域供水保障和用水损耗情况。"
      },
      {
        icon: "mdi:cog",
        title: "水资源优化配置",
        description: "结合各行业用户历史用水行为预测需水量，统筹区域多种水源可供水量，综合考虑用水优先级和用水效益，基于水资源优化配置模型。"
      },
      {
        icon: "mdi:robot",
        title: "水资源智能助手",
        description: "基于水资源知识库，开发水资源智能助手，在系统理解水资源管理工作基础上，支持用户以自然语言交互方式筛选和查询数据。"
      },
      {
        icon: "mdi:shield-check",
        title: "国产化安全架构",
        description: "采用微服务框架开发，适配国产操作系统、中间件和数据库，完全自主可控；可根据个性化业务场景进行二次开发和定制。"
      }
    ],
    3: [
      {
        icon: "mdi:book-multiple",
        title: "河湖大百科知识库",
        description: "收集河湖自然地理、社会经济、历史文化、管理制度、开发利用等各类信息，形成河湖知识库，面向各级管理人员和社会公众提供河湖知识问答服务。"
      },
      {
        icon: "mdi:eye",
        title: "河湖动态立体感知",
        description: "融合空天地水工多渠道采集的河湖遥感、视频、图片、文本、表格等多种格式数据，构建河湖数字孪生体，形象描绘和直观展示河湖动态。"
      },
      {
        icon: "mdi:brain",
        title: "智能模型应用支撑",
        description: "基于智能模型的推理分析、图像识别、语义理解、视频生成、趋势预测等能力，辅助河湖水质溯源、问题分析、履职评价等工作的开展。"
      },
      {
        icon: "mdi:shield-check",
        title: "国产架构安全可控",
        description: "采用微服务框架开发，适配国产软硬件环境，完全自主可控；提供标准服务接口方便系统对接和数据集成。"
      }
    ]
  };

  return highlights[product.id] || [];
};

const handleConsultation = () => {
  console.log("申请产品咨询");
  // 这里可以打开咨询表单
};

const handleDemo = () => {
  console.log("申请产品演示");
  // 这里可以打开演示申请表单
};

// 页面加载动画
onMounted(() => {
  setTimeout(() => {
    isLoaded.value = true;
  }, 100);

  // 数字动画效果
  const animateNumbers = () => {
    const numbers = document.querySelectorAll(".stat-number[data-count]");
    numbers.forEach((number) => {
      const target = parseInt(number.getAttribute("data-count"));
      let current = 0;
      const increment = target / 50;
      const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
          current = target;
          clearInterval(timer);
        }
        number.textContent = Math.floor(current);
      }, 50);
    });
  };

  setTimeout(animateNumbers, 500);
});
</script>
<style lang="scss" scoped>
@import url("@/assets/styles/products.scss");
</style>
