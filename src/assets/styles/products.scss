@use "./variables.scss" as *;

.products {
  min-height: 100vh;
  color: var(--gray-800);
  overflow-x: hidden;

  // 页面加载动画
  opacity: 0;
  transition: opacity var(--duration-700) var(--ease-out);

  &.loaded {
    opacity: 1;

    .fade-in-up {
      opacity: 1;
      transform: translateY(0);

      &.delay-1 {
        animation-delay: 0.1s;
      }
      &.delay-2 {
        animation-delay: 0.2s;
      }
      &.delay-3 {
        animation-delay: 0.3s;
      }
      &.delay-4 {
        animation-delay: 0.4s;
      }
    }
  }
}

// 通用动画类
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s var(--ease-smooth) forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 通用容器
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);

  @media (max-width: 768px) {
    padding: 0 var(--space-4);
  }
}

// 英雄区域
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;

  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-depth);
    z-index: -1;
  }

  .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      var(--primary-alpha-90) 0%,
      var(--corporate-blue) 100%
    );
    z-index: -1;
  }

  .hero-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .particle {
      position: absolute;
      background: var(--gradient-neon);
      border-radius: 50%;
      opacity: 0.3;

      &.particle-1 {
        width: 4px;
        height: 4px;
        top: 15%;
        left: 20%;
        @include floating-animation(3.2s, 12px, 0s);
      }
      &.particle-2 {
        width: 6px;
        height: 6px;
        top: 25%;
        left: 80%;
        @include floating-animation(4.1s, 18px, 0.5s);
      }
      &.particle-3 {
        width: 3px;
        height: 3px;
        top: 45%;
        left: 10%;
        @include floating-animation(3.8s, 15px, 1s);
      }
      &.particle-4 {
        width: 7px;
        height: 7px;
        top: 65%;
        left: 70%;
        @include floating-animation(4.5s, 22px, 1.5s);
      }
      &.particle-5 {
        width: 5px;
        height: 5px;
        top: 80%;
        left: 30%;
        @include floating-animation(3.6s, 14px, 2s);
      }
      &.particle-6 {
        width: 4px;
        height: 4px;
        top: 35%;
        left: 85%;
        @include floating-animation(4.2s, 16px, 2.5s);
      }
      &.particle-7 {
        width: 6px;
        height: 6px;
        top: 55%;
        left: 15%;
        @include floating-animation(3.9s, 20px, 0.8s);
      }
      &.particle-8 {
        width: 3px;
        height: 3px;
        top: 75%;
        left: 60%;
        @include floating-animation(4.3s, 11px, 1.8s);
      }
      &.particle-9 {
        width: 5px;
        height: 5px;
        top: 20%;
        left: 45%;
        @include floating-animation(3.7s, 17px, 2.2s);
      }
      &.particle-10 {
        width: 7px;
        height: 7px;
        top: 40%;
        left: 75%;
        @include floating-animation(4s, 19px, 1.2s);
      }
      &.particle-11 {
        width: 4px;
        height: 4px;
        top: 60%;
        left: 25%;
        @include floating-animation(4.4s, 13px, 0.3s);
      }
      &.particle-12 {
        width: 6px;
        height: 6px;
        top: 85%;
        left: 55%;
        @include floating-animation(3.5s, 21px, 2.8s);
      }
    }
  }

  .hero-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    grid-template-rows: repeat(5, 1fr);
    opacity: 0.1;

    .grid-item {
      border: 1px solid var(--primary-alpha-20);
      transition: var(--transition-all);

      &:hover {
        background: var(--primary-alpha-10);
      }
    }
  }

  .container {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: var(--space-12);
      text-align: center;
    }
  }

  .hero-content {
    color: var(--white);
  }

  .hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: var(--primary-alpha-20);
    border: 1px solid var(--primary-alpha-40);
    border-radius: var(--radius-2xl);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    margin-bottom: var(--space-8);
    @include glass-morphism(0.15, 16px, 0.3);

    .badge-icon {
      color: var(--electric-blue);
      font-size: var(--text-lg);
    }
  }

  .hero-title {
    font-size: var(--text-6xl);
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    margin-bottom: var(--space-8);

    .title-line {
      display: block;
      margin-bottom: var(--space-2);
    }

    .title-highlight {
      @include gradient-text(var(--gradient-neon));
    }

    @media (max-width: 768px) {
      font-size: var(--text-4xl);
    }
  }

  .hero-subtitle {
    font-size: var(--text-xl);
    line-height: var(--leading-relaxed);
    color: var(--sky-blue);
    margin-bottom: var(--space-10);

    strong {
      color: var(--white);
      @include gradient-text(var(--gradient-innovation));
    }
  }

  .hero-stats {
    display: flex;
    align-items: center;
    gap: var(--space-8);

    @media (max-width: 640px) {
      flex-direction: column;
      gap: var(--space-6);
    }

    .stat-item {
      text-align: center;

      .stat-icon {
        font-size: var(--text-2xl);
        color: var(--electric-blue);
        margin-bottom: var(--space-2);
      }

      .stat-number {
        font-size: var(--text-4xl);
        font-weight: var(--font-bold);
        color: var(--electric-blue);
        margin-bottom: var(--space-1);
        text-shadow: 0 0 10px var(--electric-blue);
      }

      .stat-label {
        font-size: var(--text-sm);
        color: var(--sky-blue);
        font-weight: var(--font-medium);
      }
    }

    .stat-divider {
      width: 1px;
      height: 60px;
      background: var(--primary-alpha-40);

      @media (max-width: 640px) {
        width: 60px;
        height: 1px;
      }
    }
  }

  // 视觉区域
  .hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 600px;

    @media (max-width: 968px) {
      height: 400px;
      order: -1;
    }
  }

  .visual-container {
    position: relative;
    width: 500px;
    height: 500px;

    @media (max-width: 968px) {
      width: 350px;
      height: 350px;
    }
  }

  .central-hub {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .hub-core {
      position: relative;
      width: 120px;
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;

      .core-icon {
        font-size: 60px;
        color: var(--electric-blue);
        z-index: 3;
        position: relative;
        @include floating-animation(3s, 8px);
        filter: drop-shadow(0 0 30px var(--electric-blue));
      }

      .core-pulse {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 120px;
        height: 120px;
        background: radial-gradient(
          circle,
          var(--electric-blue) 0%,
          transparent 70%
        );
        border-radius: 50%;
        opacity: 0.3;
        animation: pulse 2s infinite;
      }
    }

    .hub-ring {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border: 2px solid;
      border-radius: 50%;
      opacity: 0.6;

      &.ring-inner {
        width: 160px;
        height: 160px;
        border-color: var(--tech-alpha-50);
        animation: rotate 15s linear infinite;
      }

      &.ring-middle {
        width: 240px;
        height: 240px;
        border-color: var(--primary-alpha-30);
        animation: rotate 25s linear infinite reverse;
      }

      &.ring-outer {
        width: 320px;
        height: 320px;
        border-color: var(--electric-blue);
        opacity: 0.3;
        animation: rotate 35s linear infinite;
      }
    }
  }

  .product-constellation {
    .constellation-node {
      position: absolute;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--space-2);

      .node-icon {
        width: 50px;
        height: 50px;
        background: var(--gradient-glass);
        @include glass-morphism(0.2, 12px, 0.3);
        border-radius: var(--radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--electric-blue);
        font-size: var(--text-xl);
        transition: var(--transition-all);

        &:hover {
          transform: scale(1.1);
          box-shadow: var(--glow-neon);
        }
      }

      .node-label {
        font-size: var(--text-xs);
        color: var(--sky-blue);
        font-weight: var(--font-medium);
        text-align: center;
        white-space: nowrap;
      }

      &.node-1 {
        top: 15%;
        left: 25%;
        @include floating-animation(4s, 12px, 0s);
      }

      &.node-2 {
        top: 20%;
        right: 20%;
        @include floating-animation(3.5s, 10px, 0.5s);
      }

      &.node-3 {
        right: 10%;
        top: 50%;
        @include floating-animation(4.2s, 15px, 1s);
      }

      &.node-4 {
        bottom: 20%;
        right: 25%;
        @include floating-animation(3.8s, 11px, 1.5s);
      }

      &.node-5 {
        bottom: 15%;
        left: 20%;
        @include floating-animation(4.1s, 13px, 2s);
      }

      &.node-6 {
        left: 10%;
        top: 45%;
        @include floating-animation(3.7s, 9px, 2.5s);
      }
    }
  }

  .connection-web {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    .connection-svg {
      width: 100%;
      height: 100%;

      .connection-path {
        stroke: var(--electric-blue);
        stroke-width: 1;
        stroke-dasharray: 5, 5;
        opacity: 0.4;
        animation: dash 3s linear infinite;
      }
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes dash {
  to {
    stroke-dashoffset: -20;
  }
}

// 产品分类导航区域
.categories-section {
  @include section-padding(var(--space-16), var(--space-8));
  background: var(--gradient-subtle);

  .categories-header {
    text-align: center;
    margin-bottom: var(--space-12);

    .categories-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .categories-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .categories-grid {
    @include responsive-grid(auto-fit, 280px, var(--space-6));
  }

  .category-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-md);
    border: 2px solid var(--gray-200);
    cursor: pointer;
    transition: var(--transition-all);
    position: relative;
    overflow: hidden;

    @include card-hover(1.02, -4px);

    &.active {
      border-color: var(--primary-blue);
      background: var(--gradient-primary);
      color: var(--white);
      box-shadow: var(--shadow-lg);

      .category-content {
        color: var(--white);

        .category-name {
          color: var(--white);
        }

        .category-desc {
          color: rgba(255, 255, 255, 0.9);
        }

        .category-count {
          color: var(--electric-blue);
        }
      }

      .category-visual .category-icon {
        color: var(--white);
      }

      .category-indicator {
        color: var(--white);
      }
    }

    .category-visual {
      position: relative;
      margin-bottom: var(--space-4);
      text-align: center;

      .category-icon {
        width: 60px;
        height: 60px;
        background: var(--gradient-ocean);
        border-radius: var(--radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: var(--text-2xl);
        margin: 0 auto;
        box-shadow: var(--glow-primary);
        transition: var(--transition-all);
      }

      .category-bg {
        position: absolute;
        top: -10px;
        right: -10px;
        width: 80px;
        height: 80px;
        background: var(--gradient-glass);
        border-radius: 50%;
        opacity: 0.3;
        z-index: -1;
      }
    }

    .category-content {
      text-align: center;

      .category-name {
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        color: var(--gray-900);
        margin-bottom: var(--space-2);
      }

      .category-desc {
        color: var(--gray-600);
        font-size: var(--text-sm);
        margin-bottom: var(--space-3);
      }

      .category-count {
        font-size: var(--text-sm);
        font-weight: var(--font-bold);
        color: var(--primary-blue);
      }
    }

    .category-indicator {
      position: absolute;
      top: var(--space-4);
      right: var(--space-4);
      color: var(--gray-400);
      font-size: var(--text-lg);
      transition: var(--transition-all);
    }

    &:hover {
      .category-indicator {
        transform: translateX(4px);
      }
    }
  }
}

// 产品展示区
.products-showcase {
  @include section-padding(var(--space-20), var(--space-20));

  .showcase-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-12);

    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--space-4);
      text-align: center;
    }

    .showcase-title {
      font-size: var(--text-3xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      @include gradient-text(var(--gradient-primary));
    }

    .showcase-meta {
      display: flex;
      align-items: center;
      gap: var(--space-6);

      .product-count {
        color: var(--gray-600);
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
      }

      .view-toggle {
        display: flex;
        background: var(--gray-100);
        border-radius: var(--radius-lg);
        padding: var(--space-1);

        .toggle-btn {
          padding: var(--space-2) var(--space-3);
          background: transparent;
          border: none;
          border-radius: var(--radius-md);
          color: var(--gray-600);
          cursor: pointer;
          transition: var(--transition-all);
          font-size: var(--text-lg);

          &.active {
            background: var(--white);
            color: var(--primary-blue);
            box-shadow: var(--shadow-sm);
          }

          &:hover:not(.active) {
            color: var(--primary-blue);
          }
        }
      }
    }
  }

  // 产品网格
  .products-grid {
    display: grid;
    gap: var(--space-8);
    margin-bottom: var(--space-16);

    &.grid {
      grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }

    &.list {
      grid-template-columns: 1fr;

      .product-card {
        display: grid;
        grid-template-columns: auto 1fr auto;
        align-items: center;
        gap: var(--space-6);

        .card-visual {
          width: 120px;
          height: 120px;
        }

        .card-footer {
          flex-direction: column;
          align-items: flex-end;
        }
      }
    }
  }

  .product-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    cursor: pointer;
    transition: var(--transition-all);

    @include card-hover(1.02, -6px);

    .card-visual {
      position: relative;
      padding: var(--space-8);
      background: var(--gradient-subtle);
      text-align: center;
      overflow: hidden;

      .product-icon {
        width: 80px;
        height: 80px;
        background: var(--gradient-primary);
        border-radius: var(--radius-2xl);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--space-4);
        color: var(--white);
        font-size: var(--text-3xl);
        box-shadow: var(--glow-primary);
        transition: var(--transition-all);
      }

      .product-badge {
        position: absolute;
        top: var(--space-4);
        right: var(--space-4);
        padding: var(--space-1) var(--space-3);
        border-radius: var(--radius-2xl);
        font-size: var(--text-xs);
        font-weight: var(--font-bold);
        color: var(--white);

        &.high {
          background: var(--gradient-innovation);
        }

        &.urgent {
          background: var(--warning-orange);
        }

        &.medium {
          background: var(--gradient-neon);
        }
      }

      .card-gradient {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 50%;
        background: linear-gradient(
          to top,
          var(--primary-alpha-10),
          transparent
        );
        opacity: 0;
        transition: var(--transition-all);
      }
    }

    &:hover {
      .card-visual {
        .product-icon {
          transform: scale(1.1);
        }

        .card-gradient {
          opacity: 1;
        }
      }
    }

    .card-content {
      padding: var(--space-6);

      .product-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--space-3);

        .product-title {
          font-size: var(--text-xl);
          font-weight: var(--font-bold);
          color: var(--gray-900);
          line-height: var(--leading-tight);
          flex: 1;
        }

        .product-status {
          display: flex;
          align-items: center;
          gap: var(--space-1);
          padding: var(--space-1) var(--space-2);
          border-radius: var(--radius-lg);
          font-size: var(--text-xs);
          font-weight: var(--font-medium);
          white-space: nowrap;

          .status-icon {
            font-size: var(--text-sm);
          }

          &.运行中 {
            background: var(--success-green);
            color: var(--white);
          }

          &.最新版 {
            background: var(--gradient-innovation);
            color: var(--white);
          }

          &.稳定版 {
            background: var(--info-blue);
            color: var(--white);
          }

          &.推广中 {
            background: var(--warning-orange);
            color: var(--white);
          }
        }
      }

      .product-description {
        color: var(--gray-600);
        line-height: var(--leading-relaxed);
        margin-bottom: var(--space-4);
        font-size: var(--text-sm);
      }

      .product-features {
        margin-bottom: var(--space-4);

        .features-title {
          font-size: var(--text-sm);
          font-weight: var(--font-bold);
          color: var(--gray-800);
          margin-bottom: var(--space-2);
        }

        .features-list {
          display: flex;
          flex-direction: column;
          gap: var(--space-1);

          .feature {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: var(--text-sm);
            color: var(--gray-700);

            .feature-icon {
              color: var(--success-green);
              font-size: var(--text-sm);
              flex-shrink: 0;
            }
          }

          .feature-more {
            font-size: var(--text-xs);
            color: var(--gray-500);
            font-style: italic;
            margin-left: var(--space-5);
          }
        }
      }

      .product-tags {
        display: flex;
        flex-wrap: wrap;
        gap: var(--space-2);
        margin-bottom: var(--space-4);

        .tag {
          background: var(--crystal-blue);
          color: var(--primary-blue);
          padding: var(--space-1) var(--space-3);
          border-radius: var(--radius-2xl);
          font-size: var(--text-xs);
          font-weight: var(--font-medium);
        }

        .tag-more {
          background: var(--gray-100);
          color: var(--gray-600);
          padding: var(--space-1) var(--space-3);
          border-radius: var(--radius-2xl);
          font-size: var(--text-xs);
          font-weight: var(--font-medium);
        }
      }
    }

    .card-footer {
      padding: 0 var(--space-6) var(--space-6);
      display: flex;
      justify-content: space-between;
      align-items: center;

      .product-metrics {
        display: flex;
        gap: var(--space-4);

        .metric {
          display: flex;
          align-items: center;
          gap: var(--space-1);
          font-size: var(--text-xs);
          color: var(--gray-500);

          svg {
            font-size: var(--text-sm);
          }
        }
      }

      .detail-btn {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-2) var(--space-4);
        background: var(--gradient-primary);
        color: var(--white);
        border: none;
        border-radius: var(--radius-lg);
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        cursor: pointer;
        transition: var(--transition-all);

        .btn-arrow {
          font-size: var(--text-sm);
          transition: var(--transition-all);
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-hover);

          .btn-arrow {
            transform: translateX(2px);
          }
        }
      }
    }
  }

  // 空状态
  .empty-state {
    text-align: center;
    padding: var(--space-20) var(--space-8);

    .empty-visual {
      position: relative;
      margin-bottom: var(--space-8);

      .empty-icon {
        font-size: 80px;
        color: var(--gray-300);
        margin-bottom: var(--space-4);
      }

      .empty-animation {
        display: flex;
        justify-content: center;
        gap: var(--space-2);

        .animation-dot {
          width: 8px;
          height: 8px;
          background: var(--primary-blue);
          border-radius: 50%;
          animation: bounce 1.4s infinite ease-in-out both;

          &:nth-child(1) {
            animation-delay: -0.32s;
          }
          &:nth-child(2) {
            animation-delay: -0.16s;
          }
          &:nth-child(3) {
            animation-delay: 0s;
          }
        }
      }
    }

    .empty-title {
      font-size: var(--text-2xl);
      font-weight: var(--font-bold);
      color: var(--gray-700);
      margin-bottom: var(--space-4);
    }

    .empty-description {
      color: var(--gray-500);
      font-size: var(--text-base);
      margin-bottom: var(--space-8);
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;
    }

    .empty-action {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-3) var(--space-6);
      background: var(--gradient-primary);
      color: var(--white);
      border: none;
      border-radius: var(--radius-lg);
      font-size: var(--text-base);
      font-weight: var(--font-medium);
      cursor: pointer;
      transition: var(--transition-all);

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-hover);
      }
    }
  }

  @keyframes bounce {
    0%,
    80%,
    100% {
      transform: scale(0);
    }
    40% {
      transform: scale(1);
    }
  }
}

// 优势区域
.advantages-section {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--gradient-subtle);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .advantages-grid {
    @include responsive-grid(auto-fit, 300px, var(--space-8));
  }

  .advantage-card {
    background: var(--white);
    padding: var(--space-8);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
    transition: var(--transition-all);

    @include card-hover(1.02, -6px);

    .advantage-visual {
      position: relative;
      margin-bottom: var(--space-6);
      display: flex;
      align-items: center;
      gap: var(--space-4);

      .advantage-number {
        font-size: var(--text-3xl);
        font-weight: var(--font-bold);
        color: var(--primary-alpha-30);
        line-height: 1;
      }

      .advantage-icon {
        width: 60px;
        height: 60px;
        background: var(--gradient-ocean);
        border-radius: var(--radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: var(--text-2xl);
        box-shadow: var(--glow-primary);
        position: relative;
        z-index: 2;
      }

      .visual-bg {
        position: absolute;
        top: -10px;
        right: -10px;
        width: 80px;
        height: 80px;
        background: var(--gradient-glass);
        border-radius: 50%;
        opacity: 0.3;
        z-index: 1;
      }
    }

    .advantage-content {
      .advantage-title {
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        color: var(--gray-900);
        margin-bottom: var(--space-3);
      }

      .advantage-description {
        color: var(--gray-600);
        line-height: var(--leading-relaxed);
        margin-bottom: var(--space-4);
        font-size: var(--text-base);
      }

      .advantage-details {
        display: flex;
        flex-direction: column;
        gap: var(--space-2);

        .detail {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          font-size: var(--text-sm);
          color: var(--gray-700);

          .detail-icon {
            color: var(--success-green);
            font-size: var(--text-sm);
            flex-shrink: 0;
          }
        }
      }
    }

    .advantage-decorator {
      position: absolute;
      bottom: -20px;
      right: -20px;
      width: 60px;
      height: 60px;
      background: var(--gradient-neon);
      border-radius: 50%;
      opacity: 0.1;
      transition: var(--transition-all);
    }

    &:hover {
      .advantage-decorator {
        opacity: 0.2;
        transform: scale(1.2);
      }
    }
  }
}

// CTA 区域
.cta-section {
  position: relative;
  @include section-padding(var(--space-20), var(--space-20));
  overflow: hidden;

  .cta-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
  }

  .cta-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      var(--primary-alpha-90) 0%,
      var(--corporate-blue) 100%
    );
  }

  .cta-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;

    .pattern-dot {
      position: absolute;
      width: 2px;
      height: 2px;
      background: var(--white);
      border-radius: 50%;

      // 手动定义一些点的位置，避免使用 random() 函数
      &:nth-child(1) {
        top: 10%;
        left: 15%;
        animation: twinkle 3s infinite 0s;
      }
      &:nth-child(2) {
        top: 25%;
        left: 80%;
        animation: twinkle 4s infinite 0.5s;
      }
      &:nth-child(3) {
        top: 45%;
        left: 20%;
        animation: twinkle 3.5s infinite 1s;
      }
      &:nth-child(4) {
        top: 65%;
        left: 70%;
        animation: twinkle 4.5s infinite 1.5s;
      }
      &:nth-child(5) {
        top: 80%;
        left: 30%;
        animation: twinkle 3.2s infinite 2s;
      }
      &:nth-child(6) {
        top: 35%;
        left: 85%;
        animation: twinkle 4.2s infinite 0.3s;
      }
      &:nth-child(7) {
        top: 55%;
        left: 10%;
        animation: twinkle 3.8s infinite 1.2s;
      }
      &:nth-child(8) {
        top: 75%;
        left: 60%;
        animation: twinkle 4.3s infinite 1.8s;
      }
      &:nth-child(9) {
        top: 20%;
        left: 45%;
        animation: twinkle 3.7s infinite 2.2s;
      }
      &:nth-child(10) {
        top: 40%;
        left: 75%;
        animation: twinkle 4s infinite 0.8s;
      }
    }
  }

  .container {
    position: relative;
    z-index: 2;
  }

  .cta-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--space-12);
    align-items: center;
    color: var(--white);

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      text-align: center;
      gap: var(--space-8);
    }

    .cta-text {
      .cta-badge {
        display: inline-flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-2) var(--space-4);
        background: var(--white-alpha-20);
        border: 1px solid var(--white-alpha-30);
        border-radius: var(--radius-2xl);
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        margin-bottom: var(--space-6);
        @include glass-morphism(0.1, 16px, 0.2);

        svg {
          font-size: var(--text-lg);
          color: var(--electric-blue);
        }
      }

      .cta-title {
        font-size: var(--text-4xl);
        font-weight: var(--font-bold);
        margin-bottom: var(--space-6);
        line-height: var(--leading-tight);

        @media (max-width: 768px) {
          font-size: var(--text-3xl);
        }
      }

      .cta-subtitle {
        font-size: var(--text-lg);
        line-height: var(--leading-relaxed);
        opacity: 0.9;
        margin-bottom: var(--space-8);

        strong {
          color: var(--electric-blue);
          font-weight: var(--font-bold);
        }
      }

      .cta-features {
        display: flex;
        gap: var(--space-6);
        margin-bottom: var(--space-8);

        @media (max-width: 640px) {
          flex-direction: column;
          gap: var(--space-4);
        }

        .cta-feature {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          font-size: var(--text-sm);
          font-weight: var(--font-medium);

          svg {
            font-size: var(--text-lg);
            color: var(--electric-blue);
          }
        }
      }
    }

    .cta-actions {
      display: flex;
      flex-direction: column;
      gap: var(--space-4);
      align-items: flex-end;

      @media (max-width: 968px) {
        align-items: center;
      }

      button {
        display: flex;
        align-items: center;
        gap: var(--space-3);
        padding: var(--space-4) var(--space-6);
        border-radius: var(--radius-2xl);
        font-size: var(--text-base);
        font-weight: var(--font-medium);
        cursor: pointer;
        transition: var(--transition-all);
        border: none;
        white-space: nowrap;

        &.large {
          padding: var(--space-5) var(--space-8);
          font-size: var(--text-lg);
        }

        .btn-icon {
          font-size: var(--text-xl);
        }

        .btn-arrow {
          font-size: var(--text-lg);
          transition: var(--transition-all);
        }

        &.btn-primary {
          background: var(--white);
          color: var(--primary-blue);

          &:hover {
            background: var(--crystal-blue);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);

            .btn-arrow {
              transform: translateX(4px);
            }
          }
        }

        &.btn-secondary {
          background: transparent;
          color: var(--white);
          border: 2px solid var(--white);

          &:hover {
            background: var(--white);
            color: var(--primary-blue);
            transform: translateY(-2px);
          }
        }
      }

      .contact-info {
        display: flex;
        flex-direction: column;
        gap: var(--space-2);
        margin-top: var(--space-4);

        .contact-item {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          font-size: var(--text-sm);
          color: rgba(255, 255, 255, 0.8);

          svg {
            font-size: var(--text-base);
            color: var(--electric-blue);
          }
        }
      }
    }
  }

  @keyframes twinkle {
    0%,
    100% {
      opacity: 0.3;
    }
    50% {
      opacity: 1;
    }
  }
}

// 响应式适配
@media (max-width: 968px) {
  .page-header {
    min-height: 50vh;

    .page-title {
      font-size: var(--text-4xl);
    }

    .page-subtitle {
      font-size: var(--text-lg);
    }
  }

  .products-showcase {
    .category-nav {
      .category-btn {
        font-size: var(--text-xs);
        padding: var(--space-2) var(--space-4);
      }
    }
  }
}

@media (max-width: 640px) {
  .page-header {
    .page-title {
      font-size: var(--text-3xl);
    }

    .page-subtitle {
      font-size: var(--text-base);
    }
  }

  .product-card {
    .product-content {
      .product-actions {
        flex-direction: column;

        button {
          flex: none;
        }
      }
    }
  }

  .tech-advantages {
    .section-header .section-title {
      font-size: var(--text-3xl);
    }
  }
}
