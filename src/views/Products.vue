<template>
  <div class="products" :class="{ loaded: isLoaded }">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-overlay"></div>
        <div class="hero-particles">
          <div v-for="i in 12" :key="i" :class="`particle particle-${i}`"></div>
        </div>
        <div class="hero-grid">
          <div v-for="i in 20" :key="i" class="grid-item"></div>
        </div>
      </div>

      <div class="container">
        <div class="hero-content">
          <div class="hero-badge fade-in-up">
            <Icon icon="mdi:package-variant-closed" class="badge-icon" />
            <span>企业级智慧水利产品矩阵</span>
          </div>

          <h1 class="hero-title fade-in-up delay-1">
            <span class="title-line">全方位数字化</span>
            <span class="title-line title-highlight">水利产品生态</span>
          </h1>

          <p class="hero-subtitle fade-in-up delay-2">
            融合IoT、大数据、AI、云计算等前沿技术<br />
            <strong>构建完整的智慧水利产品体系，赋能行业数字化转型</strong>
          </p>

          <div class="hero-stats fade-in-up delay-3">
            <div class="stat-item">
              <div class="stat-icon">
                <Icon icon="mdi:package-variant" />
              </div>
              <div class="stat-number" data-count="15">0</div>
              <div class="stat-label">核心产品</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-icon">
                <Icon icon="mdi:chart-line" />
              </div>
              <div class="stat-number" data-count="800">0</div>
              <div class="stat-label">成功案例</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-icon">
                <Icon icon="mdi:star" />
              </div>
              <div class="stat-number" data-count="99">0</div>
              <div class="stat-label">满意度%</div>
            </div>
          </div>
        </div>

        <div class="hero-visual fade-in-up delay-4">
          <div class="visual-container">
            <div class="central-hub">
              <div class="hub-core">
                <Icon icon="mdi:water-circle" class="core-icon" />
                <div class="core-pulse"></div>
              </div>
              <div class="hub-ring ring-outer"></div>
              <div class="hub-ring ring-middle"></div>
              <div class="hub-ring ring-inner"></div>
            </div>

            <div class="product-constellation">
              <div class="constellation-node node-1">
                <div class="node-icon">
                  <Icon icon="mdi:chart-line" />
                </div>
                <div class="node-label">数据分析</div>
              </div>
              <div class="constellation-node node-2">
                <div class="node-icon">
                  <Icon icon="mdi:shield-check" />
                </div>
                <div class="node-label">安全监控</div>
              </div>
              <div class="constellation-node node-3">
                <div class="node-icon">
                  <Icon icon="mdi:cloud-outline" />
                </div>
                <div class="node-label">云端服务</div>
              </div>
              <div class="constellation-node node-4">
                <div class="node-icon">
                  <Icon icon="mdi:trending-up" />
                </div>
                <div class="node-label">智能预测</div>
              </div>
              <div class="constellation-node node-5">
                <div class="node-icon">
                  <Icon icon="mdi:water-pump" />
                </div>
                <div class="node-label">设备管理</div>
              </div>
              <div class="constellation-node node-6">
                <div class="node-icon">
                  <Icon icon="mdi:chart-multiple" />
                </div>
                <div class="node-label">综合调度</div>
              </div>
            </div>

            <div class="connection-web">
              <svg class="connection-svg" viewBox="0 0 400 400">
                <g class="connections">
                  <path class="connection-path" d="M200,200 L120,80" />
                  <path class="connection-path" d="M200,200 L320,100" />
                  <path class="connection-path" d="M200,200 L350,240" />
                  <path class="connection-path" d="M200,200 L280,320" />
                  <path class="connection-path" d="M200,200 L120,320" />
                  <path class="connection-path" d="M200,200 L60,180" />
                </g>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 产品分类导航 -->
    <section class="categories-section">
      <div class="container">
        <div class="categories-header">
          <h2 class="categories-title">产品分类</h2>
          <p class="categories-subtitle">专业化产品线，满足不同场景需求</p>
        </div>

        <div class="categories-grid">
          <button
            v-for="category in categories"
            :key="category.id"
            :class="[
              'category-card',
              { active: activeCategory === category.id },
            ]"
            @click="setActiveCategory(category.id)"
          >
            <div class="category-visual">
              <div class="category-icon">
                <Icon :icon="category.icon" />
              </div>
              <div class="category-bg"></div>
            </div>
            <div class="category-content">
              <h3 class="category-name">{{ category.name }}</h3>
              <p class="category-desc">{{ category.description }}</p>
              <div class="category-count">{{ category.count }}+ 产品</div>
            </div>
            <div class="category-indicator">
              <Icon icon="mdi:arrow-right" />
            </div>
          </button>
        </div>
      </div>
    </section>

    <!-- 产品展示区域 -->
    <section class="products-showcase">
      <div class="container">
        <div class="showcase-header">
          <h2 class="showcase-title">
            {{
              categories.find((c) => c.id === activeCategory)?.name ||
              "全部产品"
            }}
          </h2>
          <div class="showcase-meta">
            <span class="product-count"
              >{{ filteredProducts.length }} 个产品</span
            >
            <div class="view-toggle">
              <button
                :class="['toggle-btn', { active: viewMode === 'grid' }]"
                @click="viewMode = 'grid'"
              >
                <Icon icon="mdi:view-grid" />
              </button>
              <button
                :class="['toggle-btn', { active: viewMode === 'list' }]"
                @click="viewMode = 'list'"
              >
                <Icon icon="mdi:view-list" />
              </button>
            </div>
          </div>
        </div>

        <div :class="['products-grid', viewMode]">
          <div
            v-for="product in filteredProducts"
            :key="product.id"
            class="product-card"
            @click="handleProductDetail(product)"
          >
            <div class="card-visual">
              <div class="product-icon">
                <Icon :icon="product.icon" />
              </div>
              <div class="product-badge" :class="product.priority">
                {{ product.badge }}
              </div>
              <div class="card-gradient"></div>
            </div>

            <div class="card-content">
              <div class="product-header">
                <h3 class="product-title">{{ product.title }}</h3>
                <div
                  class="product-status"
                  :class="product.status.toLowerCase()"
                >
                  <Icon :icon="product.statusIcon" class="status-icon" />
                  <span>{{ product.status }}</span>
                </div>
              </div>

              <p class="product-description">{{ product.description }}</p>

              <div class="product-features">
                <div class="features-title">核心功能</div>
                <div class="features-list">
                  <div
                    class="feature"
                    v-for="feature in product.features.slice(0, 3)"
                    :key="feature"
                  >
                    <Icon icon="mdi:check-circle" class="feature-icon" />
                    <span>{{ feature }}</span>
                  </div>
                  <div v-if="product.features.length > 3" class="feature-more">
                    +{{ product.features.length - 3 }} 更多功能
                  </div>
                </div>
              </div>

              <div class="product-tags">
                <span
                  class="tag"
                  v-for="tag in product.tags.slice(0, 3)"
                  :key="tag"
                  >{{ tag }}</span
                >
                <span v-if="product.tags.length > 3" class="tag-more"
                  >+{{ product.tags.length - 3 }}</span
                >
              </div>
            </div>

            <div class="card-footer">
              <div class="product-metrics">
                <div class="metric">
                  <Icon icon="mdi:eye" />
                  <span>{{ product.views || "1.2k" }}</span>
                </div>
                <div class="metric">
                  <Icon icon="mdi:star" />
                  <span>{{ product.rating || "4.8" }}</span>
                </div>
              </div>
              <button class="detail-btn">
                <span>查看详情</span>
                <Icon icon="mdi:arrow-right" class="btn-arrow" />
              </button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredProducts.length === 0" class="empty-state">
          <div class="empty-visual">
            <Icon icon="mdi:package-variant" class="empty-icon" />
            <div class="empty-animation">
              <div class="animation-dot"></div>
              <div class="animation-dot"></div>
              <div class="animation-dot"></div>
            </div>
          </div>
          <h3 class="empty-title">该分类下暂无产品</h3>
          <p class="empty-description">
            我们正在不断完善产品线，敬请期待更多创新产品
          </p>
          <button class="empty-action" @click="setActiveCategory('all')">
            <span>查看全部产品</span>
            <Icon icon="mdi:arrow-right" />
          </button>
        </div>
      </div>
    </section>

    <!-- 产品优势 -->
    <section class="advantages-section">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:star-four-points" />
            <span>核心优势</span>
          </div>
          <h2 class="section-title">为什么选择我们的产品</h2>
          <p class="section-subtitle">
            专业技术 × 创新理念 × 服务保障 = 卓越产品体验
          </p>
        </div>

        <div class="advantages-grid">
          <div
            class="advantage-card"
            v-for="(advantage, index) in advantages"
            :key="index"
          >
            <div class="advantage-visual">
              <div class="advantage-number">
                {{ String(index + 1).padStart(2, "0") }}
              </div>
              <div class="advantage-icon">
                <Icon :icon="advantage.icon" />
              </div>
              <div class="visual-bg"></div>
            </div>
            <div class="advantage-content">
              <h3 class="advantage-title">{{ advantage.title }}</h3>
              <p class="advantage-description">{{ advantage.description }}</p>
              <div class="advantage-details">
                <div
                  class="detail"
                  v-for="detail in advantage.details"
                  :key="detail"
                >
                  <Icon icon="mdi:arrow-right-circle" class="detail-icon" />
                  <span>{{ detail }}</span>
                </div>
              </div>
            </div>
            <div class="advantage-decorator"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA 区域 -->
    <section class="cta-section">
      <div class="cta-background">
        <div class="cta-overlay"></div>
        <div class="cta-pattern">
          <div v-for="i in 50" :key="i" class="pattern-dot"></div>
        </div>
      </div>

      <div class="container">
        <div class="cta-content">
          <div class="cta-text">
            <div class="cta-badge">
              <Icon icon="mdi:handshake" />
              <span>专业服务</span>
            </div>
            <h2 class="cta-title">找到适合您的解决方案了吗？</h2>
            <p class="cta-subtitle">
              我们的技术专家团队将为您提供<strong>个性化的产品咨询</strong>和<strong
                >定制化解决方案</strong
              ><br />
              助力您的数字化转型之路
            </p>
            <div class="cta-features">
              <div class="cta-feature">
                <Icon icon="mdi:account-tie" />
                <span>1对1专家咨询</span>
              </div>
              <div class="cta-feature">
                <Icon icon="mdi:clock-fast" />
                <span>24小时快速响应</span>
              </div>
              <div class="cta-feature">
                <Icon icon="mdi:shield-check" />
                <span>方案质量保证</span>
              </div>
            </div>
          </div>
          <div class="cta-actions">
            <button class="btn-primary large" @click="handleConsultation">
              <Icon icon="mdi:account-tie" class="btn-icon" />
              <span>免费产品咨询</span>
              <Icon icon="mdi:arrow-right" class="btn-arrow" />
            </button>
            <button class="btn-secondary large" @click="handleDemo">
              <Icon icon="mdi:play-circle" class="btn-icon" />
              <span>申请产品演示</span>
            </button>
            <div class="contact-info">
              <div class="contact-item">
                <Icon icon="mdi:phone" />
                <span>186-1032-4200</span>
              </div>
              <div class="contact-item">
                <Icon icon="mdi:email" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { Icon } from "@iconify/vue";
import { ref, computed, onMounted } from "vue";

defineOptions({
  name: "Products",
});

// 页面状态
const isLoaded = ref(false);
const activeCategory = ref("all");
const viewMode = ref("grid");

// 产品分类数据
const categories = ref([
  {
    id: "all",
    name: "全部产品",
    icon: "mdi:view-grid",
    description: "完整产品矩阵",
    count: 15,
  },
  {
    id: "monitoring",
    name: "智能监测",
    icon: "mdi:monitor-dashboard",
    description: "实时监控系统",
    count: 5,
  },
  {
    id: "analysis",
    name: "数据分析",
    icon: "mdi:chart-line",
    description: "大数据分析平台",
    count: 4,
  },
  {
    id: "management",
    name: "资源管理",
    icon: "mdi:water-pump",
    description: "水资源调配",
    count: 3,
  },
  {
    id: "warning",
    name: "预警系统",
    icon: "mdi:weather-flood",
    description: "防汛预警",
    count: 3,
  },
]);

// 产品数据
const allProducts = ref([
  {
    id: 1,
    title: "智慧水务综合管理平台",
    description:
      "集成物联网传感器、大数据分析、AI预测的一体化水务管理解决方案，实现水务全流程数字化管理",
    icon: "mdi:water-pump",
    category: "management",
    priority: "high",
    badge: "核心产品",
    status: "运行中",
    statusIcon: "mdi:check-circle",
    features: [
      "实时监控",
      "智能调度",
      "预测分析",
      "自动报警",
      "移动端管理",
      "数据可视化",
    ],
    tags: ["物联网", "大数据", "AI算法", "云计算", "移动应用"],
    views: "2.1k",
    rating: "4.9",
  },
  {
    id: 2,
    title: "防汛四预智能系统",
    description:
      "通过预报、预警、预演、预案的全流程闭环管理，基于AI算法提供精准的防汛决策支持",
    icon: "mdi:weather-flood",
    category: "warning",
    priority: "urgent",
    badge: "重点推荐",
    status: "最新版",
    statusIcon: "mdi:star-circle",
    features: [
      "智能预报",
      "精准预警",
      "3D预演",
      "应急预案",
      "风险评估",
      "决策支持",
    ],
    tags: ["AI预警", "应急响应", "风险评估", "决策支持"],
    views: "1.8k",
    rating: "4.8",
  },
  {
    id: 3,
    title: "水资源优化配置平台",
    description:
      "基于大数据分析和优化算法的水资源智能配置系统，提高水资源利用效率",
    icon: "mdi:chart-multiple",
    category: "analysis",
    priority: "high",
    badge: "智能优化",
    status: "稳定版",
    statusIcon: "mdi:check-circle",
    features: ["需求预测", "配置优化", "调度管理", "效益分析", "节水评估"],
    tags: ["优化算法", "资源配置", "节水技术", "效益分析"],
    views: "1.5k",
    rating: "4.7",
  },
  {
    id: 4,
    title: "河湖水质监测系统",
    description:
      "集成多参数水质传感器的实时监测网络，提供全方位的水环境监控服务",
    icon: "mdi:monitor-dashboard",
    category: "monitoring",
    priority: "medium",
    badge: "环保认证",
    status: "运行中",
    statusIcon: "mdi:check-circle",
    features: ["多参数监测", "实时传输", "超标报警", "趋势分析", "报表生成"],
    tags: ["水质监测", "传感器", "实时监控", "数据分析"],
    views: "1.3k",
    rating: "4.6",
  },
  {
    id: 5,
    title: "水利工程安全监测平台",
    description: "针对大坝、堤防等水利工程的安全监测系统，确保工程运行安全",
    icon: "mdi:shield-check",
    category: "monitoring",
    priority: "high",
    badge: "安全保障",
    status: "运行中",
    statusIcon: "mdi:check-circle",
    features: ["结构监测", "变形分析", "安全评估", "风险预警", "应急响应"],
    tags: ["安全监测", "结构分析", "风险评估", "预警系统"],
    views: "1.1k",
    rating: "4.8",
  },
  {
    id: 6,
    title: "智能灌溉控制系统",
    description:
      "基于土壤墒情和气象数据的精准灌溉系统，实现农业用水的智能化管理",
    icon: "mdi:sprinkler-variant",
    category: "management",
    priority: "medium",
    badge: "节水技术",
    status: "推广中",
    statusIcon: "mdi:trending-up",
    features: ["土壤监测", "智能控制", "节水优化", "作物管理", "数据分析"],
    tags: ["精准农业", "节水灌溉", "智能控制", "农业物联网"],
    views: "950",
    rating: "4.5",
  },
  {
    id: 7,
    title: "水利大数据分析平台",
    description: "集成多源数据的综合分析平台，提供深度数据挖掘和智能决策支持",
    icon: "mdi:chart-line",
    category: "analysis",
    priority: "high",
    badge: "数据驱动",
    status: "最新版",
    statusIcon: "mdi:star-circle",
    features: ["数据挖掘", "机器学习", "预测建模", "可视化展示", "报告生成"],
    tags: ["大数据", "机器学习", "数据挖掘", "预测分析"],
    views: "1.7k",
    rating: "4.9",
  },
  {
    id: 8,
    title: "洪水预警预报系统",
    description:
      "基于气象水文数据的洪水预警系统，提供及时准确的洪水预报预警信息",
    icon: "mdi:flood",
    category: "warning",
    priority: "urgent",
    badge: "应急必备",
    status: "运行中",
    statusIcon: "mdi:check-circle",
    features: ["洪水预报", "风险评估", "预警发布", "应急响应", "历史分析"],
    tags: ["洪水预警", "气象分析", "风险评估", "应急管理"],
    views: "1.4k",
    rating: "4.7",
  },
]);

// 优势数据
const advantages = ref([
  {
    icon: "mdi:rocket-launch",
    title: "技术领先",
    description: "采用最新的物联网、AI、大数据等前沿技术",
    details: [
      "自主研发核心算法",
      "持续技术创新",
      "专利技术保护",
      "技术标准制定参与",
    ],
  },
  {
    icon: "mdi:shield-check-outline",
    title: "质量可靠",
    description: "严格的质量管理体系确保产品稳定可靠",
    details: ["ISO质量认证", "多重测试验证", "7×24小时监控", "快速故障响应"],
  },
  {
    icon: "mdi:account-group",
    title: "专业服务",
    description: "资深专家团队提供全方位专业服务",
    details: ["15年行业经验", "专业技术团队", "一对一服务", "定制化解决方案"],
  },
  {
    icon: "mdi:trending-up",
    title: "效果显著",
    description: "帮助客户实现显著的效率提升和成本节约",
    details: [
      "效率提升30%+",
      "成本降低20%+",
      "决策准确率95%+",
      "客户满意度99%+",
    ],
  },
]);

// 计算属性 - 过滤产品
const filteredProducts = computed(() => {
  if (activeCategory.value === "all") {
    return allProducts.value;
  }
  return allProducts.value.filter(
    (product) => product.category === activeCategory.value
  );
});

// 方法
const setActiveCategory = (categoryId) => {
  activeCategory.value = categoryId;
};

const handleProductDetail = (product) => {
  console.log("查看产品详情:", product);
  // 这里可以跳转到产品详情页面或打开模态框
};

const handleConsultation = () => {
  console.log("申请产品咨询");
  // 这里可以打开咨询表单
};

const handleDemo = () => {
  console.log("申请产品演示");
  // 这里可以打开演示申请表单
};

// 页面加载动画
onMounted(() => {
  setTimeout(() => {
    isLoaded.value = true;
  }, 100);

  // 数字动画效果
  const animateNumbers = () => {
    const numbers = document.querySelectorAll(".stat-number[data-count]");
    numbers.forEach((number) => {
      const target = parseInt(number.getAttribute("data-count"));
      let current = 0;
      const increment = target / 50;
      const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
          current = target;
          clearInterval(timer);
        }
        number.textContent = Math.floor(current);
      }, 50);
    });
  };

  setTimeout(animateNumbers, 500);
});
</script>
<style lang="scss" scoped>
@import url("@/assets/styles/products.scss");
</style>
